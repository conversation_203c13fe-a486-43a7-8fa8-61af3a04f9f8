<?php

namespace App\Models;

/**
 * User Model
 * 
 * Handles user authentication and management
 */
class User extends BaseModel
{
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'username', 'email', 'password_hash', 'is_active', 'last_login'
    ];
    
    /**
     * Authenticate user
     */
    public function authenticate(string $username, string $password): ?array
    {
        $user = $this->where(['username' => $username]);
        
        if (empty($user)) {
            return null;
        }
        
        $user = $user[0];
        
        if (!password_verify($password, $user['password_hash'])) {
            return null;
        }
        
        if (!$user['is_active']) {
            return null;
        }
        
        // Update last login
        $this->update($user['id'], ['last_login' => date('Y-m-d H:i:s')]);
        
        return $user;
    }
    
    /**
     * Create new user
     */
    public function createUser(array $data): int
    {
        if (isset($data['password'])) {
            $data['password_hash'] = password_hash($data['password'], PASSWORD_DEFAULT);
            unset($data['password']);
        }
        
        return $this->insert($data);
    }
    
    /**
     * Update user password
     */
    public function updatePassword(int $userId, string $newPassword): bool
    {
        $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
        return $this->update($userId, ['password_hash' => $passwordHash]);
    }
    
    /**
     * Check if username exists
     */
    public function usernameExists(string $username): bool
    {
        $result = $this->where(['username' => $username]);
        return !empty($result);
    }
    
    /**
     * Check if email exists
     */
    public function emailExists(string $email): bool
    {
        $result = $this->where(['email' => $email]);
        return !empty($result);
    }
}
