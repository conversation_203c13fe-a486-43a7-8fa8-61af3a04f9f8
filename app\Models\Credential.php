<?php

namespace App\Models;

use App\Services\EncryptionService;

/**
 * Credential Model
 * 
 * Handles secure credential storage with encryption
 */
class Credential extends BaseModel
{
    protected $table = 'credentials';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'platform_name', 'username', 'password_encrypted', 'login_url', 'category', 'notes'
    ];
    
    private $encryptionService;
    
    public function __construct()
    {
        parent::__construct();
        $this->encryptionService = new EncryptionService();
    }
    
    /**
     * Create credential with encrypted password
     */
    public function createCredential(array $data, string $masterPassword): int
    {
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password_encrypted'] = $this->encryptionService->encrypt($data['password'], $masterPassword);
            unset($data['password']);
        }
        
        return $this->insert($data);
    }
    
    /**
     * Update credential with encrypted password
     */
    public function updateCredential(int $id, array $data, string $masterPassword): bool
    {
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password_encrypted'] = $this->encryptionService->encrypt($data['password'], $masterPassword);
            unset($data['password']);
        }
        
        return $this->update($id, $data);
    }
    
    /**
     * Get credential with decrypted password
     */
    public function getCredentialWithPassword(int $id, string $masterPassword): ?array
    {
        $credential = $this->find($id);
        
        if (!$credential) {
            return null;
        }
        
        // Decrypt password if exists
        if (!empty($credential['password_encrypted'])) {
            try {
                $credential['password'] = $this->encryptionService->decrypt(
                    $credential['password_encrypted'], 
                    $masterPassword
                );
            } catch (\Exception $e) {
                $credential['password'] = '[Decryption Failed]';
            }
        }
        
        return $credential;
    }
    
    /**
     * Get all credentials by category
     */
    public function getByCategory(string $category = null): array
    {
        if ($category) {
            return $this->where(['category' => $category]);
        }
        
        return $this->findAll();
    }
    
    /**
     * Get all unique categories
     */
    public function getCategories(): array
    {
        $stmt = $this->db->query("SELECT DISTINCT category FROM {$this->table} WHERE category IS NOT NULL AND category != '' ORDER BY category");
        return $stmt->fetchAll(\PDO::FETCH_COLUMN);
    }
    
    /**
     * Search credentials by platform name or username
     */
    public function search(string $query): array
    {
        $query = '%' . $query . '%';
        $stmt = $this->db->prepare("
            SELECT * FROM {$this->table} 
            WHERE platform_name LIKE ? OR username LIKE ? 
            ORDER BY platform_name ASC
        ");
        $stmt->execute([$query, $query]);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
    
    /**
     * Get credentials count by category
     */
    public function getCountByCategory(): array
    {
        $stmt = $this->db->query("
            SELECT 
                COALESCE(category, 'Uncategorized') as category,
                COUNT(*) as count 
            FROM {$this->table} 
            GROUP BY category 
            ORDER BY category
        ");
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
    
    /**
     * Export credentials (without passwords for security)
     */
    public function exportCredentials(): array
    {
        $stmt = $this->db->query("
            SELECT 
                platform_name,
                username,
                login_url,
                category,
                notes,
                created_at
            FROM {$this->table} 
            ORDER BY platform_name ASC
        ");
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
    
    /**
     * Validate master password by attempting to decrypt a test credential
     */
    public function validateMasterPassword(string $masterPassword): bool
    {
        // Get the first credential with encrypted password
        $stmt = $this->db->prepare("
            SELECT password_encrypted 
            FROM {$this->table} 
            WHERE password_encrypted IS NOT NULL 
            AND password_encrypted != '' 
            LIMIT 1
        ");
        $stmt->execute();
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        if (!$result) {
            // No encrypted passwords to test against
            return true;
        }
        
        try {
            $this->encryptionService->decrypt($result['password_encrypted'], $masterPassword);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
