<?php
/**
 * Database Setup Helper
 * 
 * This file helps configure the database connection
 */

if ($_POST) {
    $host = $_POST['host'] ?? '127.0.0.1';
    $port = $_POST['port'] ?? 3306;
    $username = $_POST['username'] ?? 'root';
    $password = $_POST['password'] ?? '';
    $database = $_POST['database'] ?? 'dashboard_system';
    
    // Test connection
    try {
        $dsn = "mysql:host=$host;port=$port";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
        echo "✅ Connection successful!<br>";
        
        // Create database if it doesn't exist
        try {
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "✅ Database '$database' is ready<br>";
            
            // Update the database configuration file
            $configContent = "<?php
// Auto-generated database configuration
return [
    'hostname' => '$host',
    'username' => '$username',
    'password' => '$password',
    'database' => '$database',
    'port' => $port
];";
            
            file_put_contents('db-config.php', $configContent);
            echo "✅ Configuration saved to db-config.php<br>";
            echo "<a href='index.php?route=migrate' style='background: blue; color: white; padding: 10px; text-decoration: none;'>Run Migration Now</a>";
            
        } catch (PDOException $e) {
            echo "⚠️ Could not create database: " . $e->getMessage();
        }
        
        echo "</div>";
        
    } catch (PDOException $e) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
        echo "❌ Connection failed: " . $e->getMessage();
        echo "</div>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Database Setup - Web Dashboard System</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .help { background: #f0f8ff; padding: 15px; border-left: 4px solid #007cba; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>Database Setup</h1>
    <p>Configure your database connection settings:</p>
    
    <form method="POST">
        <div class="form-group">
            <label for="host">Database Host:</label>
            <input type="text" id="host" name="host" value="<?= $_POST['host'] ?? '127.0.0.1' ?>" required>
        </div>
        
        <div class="form-group">
            <label for="port">Port:</label>
            <input type="number" id="port" name="port" value="<?= $_POST['port'] ?? '3306' ?>" required>
        </div>
        
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" value="<?= $_POST['username'] ?? 'root' ?>" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" value="<?= $_POST['password'] ?? '' ?>">
        </div>
        
        <div class="form-group">
            <label for="database">Database Name:</label>
            <input type="text" id="database" name="database" value="<?= $_POST['database'] ?? 'dashboard_system' ?>" required>
        </div>
        
        <button type="submit">Test Connection & Setup</button>
    </form>
    
    <div class="help">
        <h3>Common Laragon Settings:</h3>
        <ul>
            <li><strong>Host:</strong> 127.0.0.1 or localhost</li>
            <li><strong>Port:</strong> 3306 (default)</li>
            <li><strong>Username:</strong> root</li>
            <li><strong>Password:</strong> (usually empty)</li>
        </ul>
        
        <h3>Troubleshooting:</h3>
        <ul>
            <li>Make sure Laragon is running</li>
            <li>Check that MySQL service is started in Laragon</li>
            <li>Try restarting Laragon as Administrator</li>
            <li>Check Laragon's MySQL port in the settings</li>
        </ul>
    </div>
    
    <p><a href="db-test.php">Run Database Connection Test</a></p>
    <p><a href="direct-access.php">Back to Dashboard</a></p>
</body>
</html>
