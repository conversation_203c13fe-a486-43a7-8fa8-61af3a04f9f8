<?php

/**
 * Bootstrap Configuration
 * 
 * This file contains the bootstrap code for the Web Dashboard System
 */

// Set error reporting based on environment
if (getenv('CI_ENVIRONMENT') === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', '1');
} else {
    error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT & ~E_USER_NOTICE & ~E_USER_DEPRECATED);
    ini_set('display_errors', '0');
}

// Set default timezone
$timezone = getenv('app.timezone') ?: 'UTC';
// Remove any quotes that might be present
$timezone = trim($timezone, '"\'');
date_default_timezone_set($timezone);

// Set internal encoding
if (extension_loaded('mbstring')) {
    mb_internal_encoding('UTF-8');
}

// Load common functions
require_once APPPATH . 'Common.php';

// Initialize autoloader
spl_autoload_register(function ($class) {
    $class = ltrim($class, '\\');
    
    // Handle App namespace
    if (strpos($class, 'App\\') === 0) {
        $class = substr($class, 4);
        $file = APPPATH . str_replace('\\', DIRECTORY_SEPARATOR, $class) . '.php';
        if (file_exists($file)) {
            require_once $file;
            return true;
        }
    }
    
    return false;
});

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
