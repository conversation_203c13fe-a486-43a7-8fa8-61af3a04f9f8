<?php

namespace App\Controllers;

use App\Models\HostingInfo;

/**
 * Hosting Controller
 * 
 * Manages hosting information and cPanel integration
 */
class Hosting extends BaseController
{
    private $hostingModel;
    
    public function __construct()
    {
        $this->hostingModel = new HostingInfo();
    }
    
    /**
     * Display hosting overview
     */
    public function index()
    {
        $hostingInfo = $this->hostingModel->getActiveHosting();
        
        $data = [
            'title' => 'Hosting Overview - Web Dashboard System',
            'pageTitle' => 'Hosting Overview',
            'currentPage' => 'hosting',
            'user' => [
                'id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'email' => $_SESSION['email']
            ],
            'hostingInfo' => $hostingInfo,
            'error' => flash('error'),
            'success' => flash('success')
        ];
        
        $this->view('hosting/index', $data, 'dashboard');
    }
    
    /**
     * Show add/edit hosting form
     */
    public function form($id = null)
    {
        $hostingInfo = null;
        
        if ($id) {
            $hostingInfo = $this->hostingModel->find($id);
            if (!$hostingInfo) {
                set_flash('error', 'Hosting information not found.');
                redirect('hosting');
            }
        }
        
        $data = [
            'title' => ($id ? 'Edit' : 'Add') . ' Hosting Information - Web Dashboard System',
            'pageTitle' => ($id ? 'Edit' : 'Add') . ' Hosting Information',
            'currentPage' => 'hosting',
            'user' => [
                'id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'email' => $_SESSION['email']
            ],
            'hostingInfo' => $hostingInfo,
            'isEdit' => !empty($id),
            'error' => flash('error'),
            'success' => flash('success')
        ];
        
        $this->view('hosting/form', $data, 'dashboard');
    }
    
    /**
     * Save hosting information
     */
    public function save($id = null)
    {
        if ($this->request['method'] !== 'POST') {
            redirect('hosting');
        }
        
        // Validate CSRF token
        if (!$this->validateCSRF()) {
            set_flash('error', 'Invalid security token. Please try again.');
            redirect('hosting/form' . ($id ? '/' . $id : ''));
        }
        
        // Get form data
        $data = [
            'provider_name' => $this->sanitize($this->request['post']['provider_name'] ?? ''),
            'plan_name' => $this->sanitize($this->request['post']['plan_name'] ?? ''),
            'disk_usage' => $this->sanitize($this->request['post']['disk_usage'] ?? ''),
            'ssl_expiry' => $this->request['post']['ssl_expiry'] ?? null,
            'renewal_date' => $this->request['post']['renewal_date'] ?? null,
            'cpanel_url' => $this->sanitize($this->request['post']['cpanel_url'] ?? ''),
            'cpanel_username' => $this->sanitize($this->request['post']['cpanel_username'] ?? ''),
            'notes' => $this->sanitize($this->request['post']['notes'] ?? ''),
            'is_active' => isset($this->request['post']['is_active']) ? 1 : 0
        ];
        
        $cpanelPassword = $this->request['post']['cpanel_password'] ?? '';
        
        // Validate required fields
        $errors = $this->validateRequired($data, ['provider_name']);
        
        if (!empty($errors)) {
            set_flash('error', implode('<br>', $errors));
            redirect('hosting/form' . ($id ? '/' . $id : ''));
        }
        
        // Convert empty dates to null
        if (empty($data['ssl_expiry'])) $data['ssl_expiry'] = null;
        if (empty($data['renewal_date'])) $data['renewal_date'] = null;
        
        try {
            // Get master password from session (in real implementation, this would be more secure)
            $masterPassword = 'default_master_password'; // This should be user-provided
            
            if ($id) {
                // Update existing record
                if (!empty($cpanelPassword)) {
                    $result = $this->hostingModel->updateHostingWithCredentials($id, $data + ['cpanel_password' => $cpanelPassword], $masterPassword);
                } else {
                    $result = $this->hostingModel->update($id, $data);
                }
                
                if ($result) {
                    set_flash('success', 'Hosting information updated successfully.');
                } else {
                    set_flash('error', 'Failed to update hosting information.');
                }
            } else {
                // Create new record
                if (!empty($cpanelPassword)) {
                    $result = $this->hostingModel->createHostingWithCredentials($data + ['cpanel_password' => $cpanelPassword], $masterPassword);
                } else {
                    $result = $this->hostingModel->insert($data);
                }
                
                if ($result) {
                    set_flash('success', 'Hosting information added successfully.');
                } else {
                    set_flash('error', 'Failed to add hosting information.');
                }
            }
        } catch (\Exception $e) {
            set_flash('error', 'Error: ' . $e->getMessage());
        }
        
        redirect('hosting');
    }
    
    /**
     * Delete hosting information
     */
    public function delete($id)
    {
        if (!$id) {
            set_flash('error', 'Invalid hosting ID.');
            redirect('hosting');
        }
        
        $result = $this->hostingModel->delete($id);
        
        if ($result) {
            set_flash('success', 'Hosting information deleted successfully.');
        } else {
            set_flash('error', 'Failed to delete hosting information.');
        }
        
        redirect('hosting');
    }
    
    /**
     * Test cPanel connection (placeholder for future implementation)
     */
    public function testConnection($id)
    {
        // This would implement actual cPanel API testing
        set_flash('success', 'cPanel connection test feature coming soon.');
        redirect('hosting');
    }
}
