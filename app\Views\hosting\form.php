<!-- Flash Messages -->
<?php if (!empty($error)): ?>
    <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
        <?= $error ?>
    </div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
        <?= $success ?>
    </div>
<?php endif; ?>

<!-- Page Header -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h1 class="text-2xl font-bold text-gray-900"><?= $isEdit ? 'Edit' : 'Add' ?> Hosting Information</h1>
        <p class="text-gray-600">Manage your hosting provider details and credentials</p>
    </div>
    <a href="<?= site_url('hosting') ?>" 
       class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Hosting
    </a>
</div>

<!-- Hosting Form -->
<div class="bg-white rounded-lg shadow-md p-6">
    <form method="POST" action="<?= site_url('hosting/save' . ($isEdit ? '/' . $hostingInfo['id'] : '')) ?>" x-data="{ loading: false }" @submit="loading = true">
        
        <?= csrf_field() ?>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            
            <!-- Provider Name -->
            <div class="md:col-span-2">
                <label for="provider_name" class="block text-sm font-medium text-gray-700 mb-2">
                    Provider Name <span class="text-red-500">*</span>
                </label>
                <input type="text" 
                       id="provider_name" 
                       name="provider_name" 
                       required
                       value="<?= htmlspecialchars($hostingInfo['provider_name'] ?? old('provider_name')) ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       placeholder="e.g., Hostinger, GoDaddy, SiteGround">
            </div>
            
            <!-- Plan Name -->
            <div>
                <label for="plan_name" class="block text-sm font-medium text-gray-700 mb-2">
                    Plan Name
                </label>
                <input type="text" 
                       id="plan_name" 
                       name="plan_name" 
                       value="<?= htmlspecialchars($hostingInfo['plan_name'] ?? old('plan_name')) ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       placeholder="e.g., Business Web Hosting">
            </div>
            
            <!-- Disk Usage -->
            <div>
                <label for="disk_usage" class="block text-sm font-medium text-gray-700 mb-2">
                    Disk Usage
                </label>
                <input type="text" 
                       id="disk_usage" 
                       name="disk_usage" 
                       value="<?= htmlspecialchars($hostingInfo['disk_usage'] ?? old('disk_usage')) ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       placeholder="e.g., 15GB / 100GB">
            </div>
            
            <!-- SSL Expiry -->
            <div>
                <label for="ssl_expiry" class="block text-sm font-medium text-gray-700 mb-2">
                    SSL Expiry Date
                </label>
                <input type="date" 
                       id="ssl_expiry" 
                       name="ssl_expiry" 
                       value="<?= $hostingInfo['ssl_expiry'] ?? old('ssl_expiry') ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>
            
            <!-- Renewal Date -->
            <div>
                <label for="renewal_date" class="block text-sm font-medium text-gray-700 mb-2">
                    Hosting Renewal Date
                </label>
                <input type="date" 
                       id="renewal_date" 
                       name="renewal_date" 
                       value="<?= $hostingInfo['renewal_date'] ?? old('renewal_date') ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>
        </div>
        
        <!-- cPanel Information Section -->
        <div class="mt-8 pt-8 border-t border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">cPanel Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                
                <!-- cPanel URL -->
                <div class="md:col-span-2">
                    <label for="cpanel_url" class="block text-sm font-medium text-gray-700 mb-2">
                        cPanel URL
                    </label>
                    <input type="url" 
                           id="cpanel_url" 
                           name="cpanel_url" 
                           value="<?= htmlspecialchars($hostingInfo['cpanel_url'] ?? old('cpanel_url')) ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           placeholder="https://cpanel.yourdomain.com:2083">
                </div>
                
                <!-- cPanel Username -->
                <div>
                    <label for="cpanel_username" class="block text-sm font-medium text-gray-700 mb-2">
                        cPanel Username
                    </label>
                    <input type="text" 
                           id="cpanel_username" 
                           name="cpanel_username" 
                           value="<?= htmlspecialchars($hostingInfo['cpanel_username'] ?? old('cpanel_username')) ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Your cPanel username">
                </div>
                
                <!-- cPanel Password -->
                <div>
                    <label for="cpanel_password" class="block text-sm font-medium text-gray-700 mb-2">
                        cPanel Password
                    </label>
                    <input type="password" 
                           id="cpanel_password" 
                           name="cpanel_password" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           placeholder="<?= $isEdit ? 'Leave blank to keep current password' : 'Your cPanel password' ?>">
                    <?php if ($isEdit): ?>
                        <p class="mt-1 text-xs text-gray-500">Leave blank to keep the current password</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Notes Section -->
        <div class="mt-8 pt-8 border-t border-gray-200">
            <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                    Notes
                </label>
                <textarea id="notes" 
                          name="notes" 
                          rows="4"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Additional notes about this hosting account..."><?= htmlspecialchars($hostingInfo['notes'] ?? old('notes')) ?></textarea>
            </div>
        </div>
        
        <!-- Status -->
        <div class="mt-6">
            <div class="flex items-center">
                <input type="checkbox" 
                       id="is_active" 
                       name="is_active" 
                       value="1"
                       <?= (!isset($hostingInfo) || $hostingInfo['is_active']) ? 'checked' : '' ?>
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="is_active" class="ml-2 block text-sm text-gray-900">
                    Active hosting account
                </label>
            </div>
        </div>
        
        <!-- Form Actions -->
        <div class="mt-8 pt-8 border-t border-gray-200 flex justify-end space-x-4">
            <a href="<?= site_url('hosting') ?>" 
               class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-lg">
                Cancel
            </a>
            <button type="submit" 
                    :disabled="loading"
                    :class="loading ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'"
                    class="text-white px-6 py-2 rounded-lg flex items-center">
                
                <span x-show="!loading"><?= $isEdit ? 'Update' : 'Save' ?> Hosting Information</span>
                <span x-show="loading" x-cloak class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <?= $isEdit ? 'Updating...' : 'Saving...' ?>
                </span>
            </button>
        </div>
    </form>
</div>

<!-- Security Notice -->
<div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
    <div class="flex">
        <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <div>
            <h3 class="text-sm font-medium text-yellow-800">Security Notice</h3>
            <p class="mt-1 text-sm text-yellow-700">
                All sensitive information including passwords are encrypted using AES-256 encryption before being stored in the database.
                Your data is protected with industry-standard security measures.
            </p>
        </div>
    </div>
</div>
