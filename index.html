<!DOCTYPE html>
<html>
<head>
    <title>Web Dashboard System - Access Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: green; background: #f0f8f0; padding: 15px; border-left: 4px solid green; }
        .info { color: blue; background: #f0f8ff; padding: 15px; border-left: 4px solid blue; }
        .warning { color: orange; background: #fff8f0; padding: 15px; border-left: 4px solid orange; }
        a { color: #007cba; text-decoration: none; padding: 10px; background: #f0f8ff; border-radius: 4px; display: inline-block; margin: 5px; }
        a:hover { background: #e0e8ff; }
    </style>
</head>
<body>
    <h1>🎉 Web Dashboard System - Access Test</h1>
    
    <div class="success">
        <h3>✅ Basic Web Access Working!</h3>
        <p>If you can see this page, Apache is serving files from the dashboard directory.</p>
    </div>
    
    <div class="info">
        <h3>📋 Next Steps:</h3>
        <p>Try these links to test different parts of the system:</p>
        
        <a href="test.php">PHP Test</a>
        <a href="db-test.php">Database Test</a>
        <a href="setup-database.php">Database Setup</a>
        <a href="direct-access.php">Direct Access Helper</a>
    </div>
    
    <div class="warning">
        <h3>⚠️ If PHP links don't work:</h3>
        <p>The .htaccess file might be causing issues. Follow these steps:</p>
        <ol>
            <li>Rename .htaccess to .htaccess.backup</li>
            <li>Try the PHP links again</li>
            <li>If they work, there's an .htaccess configuration issue</li>
        </ol>
    </div>
    
    <div class="info">
        <h3>🔧 Manual Access URLs:</h3>
        <p>If URL rewriting doesn't work, use these direct URLs:</p>
        
        <a href="index.php?route=migrate">Run Migration</a>
        <a href="index.php?route=auth/login">Login Page</a>
        <a href="index.php?route=dashboard">Dashboard</a>
        <a href="index.php?route=hosting">Hosting Module</a>
        <a href="index.php?route=credentials">Credentials Module</a>
    </div>
    
    <hr>
    
    <h3>🛠️ Troubleshooting Guide:</h3>
    
    <h4>Step 1: Check Laragon</h4>
    <ul>
        <li>Make sure Laragon is running</li>
        <li>Apache service should be green/started</li>
        <li>Try restarting Apache in Laragon</li>
    </ul>
    
    <h4>Step 2: Fix .htaccess Issues</h4>
    <ul>
        <li>Rename .htaccess to .htaccess.backup</li>
        <li>Test if PHP files work without .htaccess</li>
        <li>If they work, the issue is in .htaccess configuration</li>
    </ul>
    
    <h4>Step 3: Enable mod_rewrite</h4>
    <ul>
        <li>Open Laragon → Apache → httpd.conf</li>
        <li>Find: #LoadModule rewrite_module modules/mod_rewrite.so</li>
        <li>Remove the # to uncomment it</li>
        <li>Restart Apache</li>
    </ul>
    
    <h4>Step 4: Virtual Host (Advanced)</h4>
    <ul>
        <li>In Laragon, go to Apache → sites-enabled</li>
        <li>Create a new .conf file for the dashboard</li>
        <li>Or use the auto virtual host feature</li>
    </ul>
    
    <p><strong>Current Time:</strong> <script>document.write(new Date().toLocaleString());</script></p>
</body>
</html>
