@echo off
echo ========================================
echo Web Dashboard System - Apache Fix
echo ========================================
echo.

echo Step 1: Backing up .htaccess file...
if exist .htaccess (
    copy .htaccess .htaccess.backup
    echo ✅ .htaccess backed up to .htaccess.backup
) else (
    echo ⚠️ No .htaccess file found
)

echo.
echo Step 2: Temporarily disabling .htaccess...
if exist .htaccess (
    ren .htaccess .htaccess.disabled
    echo ✅ .htaccess disabled (renamed to .htaccess.disabled)
) else (
    echo ⚠️ No .htaccess file to disable
)

echo.
echo Step 3: Setting directory permissions...
icacls . /grant Everyone:(OI)(CI)F /T >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Directory permissions set
) else (
    echo ⚠️ Could not set permissions (try running as Administrator)
)

echo.
echo Step 4: Creating simple .htaccess for testing...
echo # Simple .htaccess for testing > .htaccess.simple
echo DirectoryIndex index.html index.php >> .htaccess.simple
echo. >> .htaccess.simple
echo ^<Directory "%CD%"^> >> .htaccess.simple
echo     AllowOverride All >> .htaccess.simple
echo     Require all granted >> .htaccess.simple
echo ^</Directory^> >> .htaccess.simple
echo ✅ Created .htaccess.simple

echo.
echo ========================================
echo Fix Complete! Next Steps:
echo ========================================
echo 1. Try accessing: http://localhost/dashboard/index.html
echo 2. If that works, try: http://localhost/dashboard/test.php
echo 3. If PHP works, try: http://localhost/dashboard/index.php
echo 4. To restore URL rewriting: copy .htaccess.simple .htaccess
echo.
echo If you still get "Forbidden" errors:
echo - Restart Laragon as Administrator
echo - Check Laragon Apache logs
echo - Try creating a virtual host in Laragon
echo.
pause
