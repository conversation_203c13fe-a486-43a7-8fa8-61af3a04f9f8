<?php

namespace App\Controllers;

use App\Models\User;

/**
 * Authentication Controller
 * 
 * Handles user authentication for the Web Dashboard System
 */
class Auth extends BaseController
{
    /**
     * Override authentication check for auth routes
     */
    protected function checkAuthentication()
    {
        // Allow auth routes without authentication
    }
    
    /**
     * Show login form
     */
    public function login()
    {
        // Redirect if already authenticated
        if ($this->isAuthenticated()) {
            redirect('dashboard');
        }
        
        $data = [
            'title' => 'Login - Web Dashboard System',
            'error' => flash('error'),
            'success' => flash('success')
        ];
        
        $this->view('auth/login', $data);
    }
    
    /**
     * Process login
     */
    public function authenticate()
    {
        if ($this->request['method'] !== 'POST') {
            redirect('auth/login');
        }
        
        // Validate CSRF token
        if (!$this->validateCSRF()) {
            set_flash('error', 'Invalid security token. Please try again.');
            redirect('auth/login');
        }
        
        $username = $this->sanitize($this->request['post']['username'] ?? '');
        $password = $this->request['post']['password'] ?? '';
        
        // Validate required fields
        $errors = $this->validateRequired(['username' => $username, 'password' => $password], ['username', 'password']);
        
        if (!empty($errors)) {
            set_flash('error', implode('<br>', $errors));
            redirect('auth/login');
        }
        
        // Attempt authentication
        $userModel = new User();
        $user = $userModel->authenticate($username, $password);
        
        if (!$user) {
            set_flash('error', 'Invalid username or password.');
            redirect('auth/login');
        }
        
        // Set session data
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['logged_in_at'] = time();
        
        // Regenerate session ID for security
        session_regenerate_id(true);
        
        set_flash('success', 'Welcome back, ' . $user['username'] . '!');
        redirect('dashboard');
    }
    
    /**
     * Logout user
     */
    public function logout()
    {
        // Clear session data
        session_unset();
        session_destroy();
        
        // Start new session
        session_start();
        
        set_flash('success', 'You have been logged out successfully.');
        redirect('auth/login');
    }
    
    /**
     * Show change password form
     */
    public function changePassword()
    {
        $data = [
            'title' => 'Change Password - Web Dashboard System',
            'error' => flash('error'),
            'success' => flash('success')
        ];
        
        $this->view('auth/change_password', $data);
    }
    
    /**
     * Process password change
     */
    public function updatePassword()
    {
        if ($this->request['method'] !== 'POST') {
            redirect('auth/change-password');
        }
        
        // Validate CSRF token
        if (!$this->validateCSRF()) {
            set_flash('error', 'Invalid security token. Please try again.');
            redirect('auth/change-password');
        }
        
        $currentPassword = $this->request['post']['current_password'] ?? '';
        $newPassword = $this->request['post']['new_password'] ?? '';
        $confirmPassword = $this->request['post']['confirm_password'] ?? '';
        
        // Validate required fields
        $errors = $this->validateRequired([
            'current_password' => $currentPassword,
            'new_password' => $newPassword,
            'confirm_password' => $confirmPassword
        ], ['current_password', 'new_password', 'confirm_password']);
        
        if (!empty($errors)) {
            set_flash('error', implode('<br>', $errors));
            redirect('auth/change-password');
        }
        
        // Validate password confirmation
        if ($newPassword !== $confirmPassword) {
            set_flash('error', 'New password and confirmation do not match.');
            redirect('auth/change-password');
        }
        
        // Validate password strength
        if (strlen($newPassword) < 8) {
            set_flash('error', 'New password must be at least 8 characters long.');
            redirect('auth/change-password');
        }
        
        // Verify current password
        $userModel = new User();
        $user = $userModel->find($_SESSION['user_id']);
        
        if (!password_verify($currentPassword, $user['password_hash'])) {
            set_flash('error', 'Current password is incorrect.');
            redirect('auth/change-password');
        }
        
        // Update password
        $result = $userModel->updatePassword($_SESSION['user_id'], $newPassword);
        
        if ($result) {
            set_flash('success', 'Password updated successfully.');
        } else {
            set_flash('error', 'Failed to update password. Please try again.');
        }
        
        redirect('auth/change-password');
    }
    
    /**
     * Check if user is authenticated (override parent method)
     */
    protected function isAuthenticated(): bool
    {
        return isset($_SESSION['user_id']) && 
               !empty($_SESSION['user_id']) && 
               isset($_SESSION['logged_in_at']) &&
               (time() - $_SESSION['logged_in_at']) < 7200; // 2 hours session timeout
    }
}
