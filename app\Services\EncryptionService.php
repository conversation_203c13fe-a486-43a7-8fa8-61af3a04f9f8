<?php

namespace App\Services;

/**
 * Encryption Service
 * 
 * Provides AES-256 encryption/decryption for sensitive data
 * Compatible with shared hosting environments
 */
class EncryptionService
{
    private const CIPHER = 'AES-256-CBC';
    private const HASH_ALGO = 'sha256';
    
    /**
     * Encrypt data using AES-256-CBC
     */
    public function encrypt(string $data, string $masterPassword): string
    {
        if (empty($data)) {
            return '';
        }
        
        // Generate a random salt
        $salt = random_bytes(16);
        
        // Derive key from master password and salt
        $key = $this->deriveKey($masterPassword, $salt);
        
        // Generate random IV
        $iv = random_bytes(openssl_cipher_iv_length(self::CIPHER));
        
        // Encrypt the data
        $encrypted = openssl_encrypt($data, self::CIPHER, $key, OPENSSL_RAW_DATA, $iv);
        
        if ($encrypted === false) {
            throw new \Exception('Encryption failed');
        }
        
        // Combine salt, IV, and encrypted data
        $result = $salt . $iv . $encrypted;
        
        // Return base64 encoded result
        return base64_encode($result);
    }
    
    /**
     * Decrypt data using AES-256-CBC
     */
    public function decrypt(string $encryptedData, string $masterPassword): string
    {
        if (empty($encryptedData)) {
            return '';
        }
        
        try {
            // Decode base64
            $data = base64_decode($encryptedData, true);
            
            if ($data === false) {
                throw new \Exception('Invalid base64 data');
            }
            
            // Extract salt (first 16 bytes)
            $salt = substr($data, 0, 16);
            
            // Extract IV (next 16 bytes)
            $ivLength = openssl_cipher_iv_length(self::CIPHER);
            $iv = substr($data, 16, $ivLength);
            
            // Extract encrypted data (remaining bytes)
            $encrypted = substr($data, 16 + $ivLength);
            
            // Derive key from master password and salt
            $key = $this->deriveKey($masterPassword, $salt);
            
            // Decrypt the data
            $decrypted = openssl_decrypt($encrypted, self::CIPHER, $key, OPENSSL_RAW_DATA, $iv);
            
            if ($decrypted === false) {
                throw new \Exception('Decryption failed - invalid password or corrupted data');
            }
            
            return $decrypted;
            
        } catch (\Exception $e) {
            throw new \Exception('Decryption error: ' . $e->getMessage());
        }
    }
    
    /**
     * Derive encryption key from master password and salt
     */
    private function deriveKey(string $password, string $salt): string
    {
        // Use PBKDF2 for key derivation
        return hash_pbkdf2(self::HASH_ALGO, $password, $salt, 10000, 32, true);
    }
    
    /**
     * Generate a secure random password
     */
    public function generatePassword(int $length = 16): string
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[random_int(0, strlen($characters) - 1)];
        }
        
        return $password;
    }
    
    /**
     * Hash a master password for storage
     */
    public function hashMasterPassword(string $password): string
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * Verify a master password against its hash
     */
    public function verifyMasterPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }
    
    /**
     * Encrypt API credentials for storage
     */
    public function encryptCredentials(array $credentials, string $masterPassword): array
    {
        $encrypted = [];
        
        foreach ($credentials as $key => $value) {
            if (is_string($value) && !empty($value)) {
                $encrypted[$key] = $this->encrypt($value, $masterPassword);
            } else {
                $encrypted[$key] = $value;
            }
        }
        
        return $encrypted;
    }
    
    /**
     * Decrypt API credentials from storage
     */
    public function decryptCredentials(array $encryptedCredentials, string $masterPassword): array
    {
        $decrypted = [];
        
        foreach ($encryptedCredentials as $key => $value) {
            if (is_string($value) && !empty($value)) {
                try {
                    $decrypted[$key] = $this->decrypt($value, $masterPassword);
                } catch (\Exception $e) {
                    // If decryption fails, return empty string
                    $decrypted[$key] = '';
                }
            } else {
                $decrypted[$key] = $value;
            }
        }
        
        return $decrypted;
    }
    
    /**
     * Test encryption/decryption functionality
     */
    public function test(): array
    {
        $testData = 'Hello, World! This is a test string with special characters: !@#$%^&*()';
        $masterPassword = 'test_master_password_123';
        
        try {
            // Test encryption
            $encrypted = $this->encrypt($testData, $masterPassword);
            
            // Test decryption
            $decrypted = $this->decrypt($encrypted, $masterPassword);
            
            return [
                'success' => true,
                'original' => $testData,
                'encrypted' => $encrypted,
                'decrypted' => $decrypted,
                'match' => $testData === $decrypted
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
