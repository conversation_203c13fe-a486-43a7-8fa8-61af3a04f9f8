# Virtual Host Configuration for Web Dashboard System
# Place this file in Laragon's Apache sites-enabled directory
# Usually: C:\laragon\bin\apache\apache-2.4.x\sites-enabled\

<VirtualHost *:80>
    ServerName dashboard.local
    ServerAlias www.dashboard.local
    DocumentRoot "C:/laragon/www/dashboard"
    
    <Directory "C:/laragon/www/dashboard">
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
        
        # Enable URL rewriting
        RewriteEngine On
        
        # Handle front controller
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>
    
    # Security headers
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
    
    # Prevent access to sensitive files
    <Files ".env">
        Require all denied
    </Files>
    
    <Files "*.log">
        Require all denied
    </Files>
    
    # Error and access logs
    ErrorLog "C:/laragon/www/dashboard/logs/error.log"
    CustomLog "C:/laragon/www/dashboard/logs/access.log" combined
</VirtualHost>

# Alternative configuration for subdirectory access
# Use this if you prefer http://localhost/dashboard/ instead of dashboard.local

<Directory "C:/laragon/www/dashboard">
    Options Indexes FollowSymLinks
    AllowOverride All
    Require all granted
    DirectoryIndex index.php index.html
</Directory>
