<?php

/**
 * Common Functions
 * 
 * Global helper functions for the Web Dashboard System
 */

if (!function_exists('env')) {
    /**
     * Get environment variable with optional default
     */
    function env(string $key, $default = null)
    {
        $value = getenv($key);
        
        if ($value === false) {
            return $default;
        }
        
        // Convert string representations of boolean values
        switch (strtolower($value)) {
            case 'true':
            case '(true)':
                return true;
            case 'false':
            case '(false)':
                return false;
            case 'empty':
            case '(empty)':
                return '';
            case 'null':
            case '(null)':
                return null;
        }
        
        return $value;
    }
}

if (!function_exists('base_url')) {
    /**
     * Generate base URL
     */
    function base_url(string $uri = ''): string
    {
        $baseURL = env('app.baseURL', 'http://localhost/');
        return rtrim($baseURL, '/') . '/' . ltrim($uri, '/');
    }
}

if (!function_exists('site_url')) {
    /**
     * Generate site URL
     */
    function site_url(string $uri = ''): string
    {
        return base_url($uri);
    }
}

if (!function_exists('redirect')) {
    /**
     * Redirect to URL
     */
    function redirect(string $uri)
    {
        header('Location: ' . site_url($uri));
        exit;
    }
}

if (!function_exists('csrf_token')) {
    /**
     * Generate CSRF token
     */
    function csrf_token(): string
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}

if (!function_exists('csrf_field')) {
    /**
     * Generate CSRF hidden input field
     */
    function csrf_field(): string
    {
        return '<input type="hidden" name="csrf_token" value="' . csrf_token() . '">';
    }
}

if (!function_exists('old')) {
    /**
     * Get old input value
     */
    function old(string $key, $default = '')
    {
        return $_SESSION['old_input'][$key] ?? $default;
    }
}

if (!function_exists('flash')) {
    /**
     * Get flash message
     */
    function flash(string $key = null)
    {
        if ($key === null) {
            return $_SESSION['flash_data'] ?? [];
        }
        
        $value = $_SESSION['flash_data'][$key] ?? null;
        unset($_SESSION['flash_data'][$key]);
        return $value;
    }
}

if (!function_exists('set_flash')) {
    /**
     * Set flash message
     */
    function set_flash(string $key, $value)
    {
        $_SESSION['flash_data'][$key] = $value;
    }
}
