<?php

namespace App\Config;

/**
 * Database Configuration
 * 
 * Database connection settings for the Web Dashboard System
 */
class Database
{
    public array $default = [
        'hostname' => 'localhost',
        'username' => 'root',
        'password' => '',
        'database' => 'dashboard_system',
        'DBDriver' => 'MySQLi',
        'DBPrefix' => '',
        'pConnect' => false,
        'DBDebug'  => true,
        'charset'  => 'utf8mb4',
        'DBCollat' => 'utf8mb4_general_ci',
        'swapPre'  => '',
        'encrypt'  => false,
        'compress' => false,
        'strictOn' => false,
        'failover' => [],
        'port'     => 3306,
    ];

    public function __construct()
    {
        // Load database settings from environment or use defaults
        $this->default['hostname'] = $this->getEnv('database.default.hostname', 'localhost');
        $this->default['username'] = $this->getEnv('database.default.username', 'root');
        $this->default['password'] = $this->getEnv('database.default.password', '');
        $this->default['database'] = $this->getEnv('database.default.database', 'dashboard_system');
        $this->default['DBDriver'] = $this->getEnv('database.default.DBDriver', 'MySQLi');
        $this->default['DBPrefix'] = $this->getEnv('database.default.DBPrefix', '');
        $this->default['port'] = (int) $this->getEnv('database.default.port', 3306);
    }

    /**
     * Get environment variable with fallback
     */
    private function getEnv(string $key, $default = null)
    {
        // Try to load from .env file if it exists
        static $envLoaded = false;
        static $envVars = [];

        if (!$envLoaded) {
            $envFile = FCPATH . '.env';
            if (file_exists($envFile)) {
                $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                foreach ($lines as $line) {
                    if (strpos($line, '=') !== false && !str_starts_with(trim($line), '#')) {
                        list($name, $value) = explode('=', $line, 2);
                        $envVars[trim($name)] = trim($value, '"\'');
                    }
                }
            }
            $envLoaded = true;
        }

        return $envVars[$key] ?? $default;
    }

    /**
     * Get database connection
     */
    public function getConnection(string $group = 'default')
    {
        $config = $this->default;
        
        if ($group !== 'default' && isset($this->$group)) {
            $config = $this->$group;
        }

        try {
            $dsn = "mysql:host={$config['hostname']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
            $pdo = new \PDO($dsn, $config['username'], $config['password'], [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                \PDO::ATTR_EMULATE_PREPARES => false,
            ]);
            
            return $pdo;
        } catch (\PDOException $e) {
            if (env('CI_ENVIRONMENT') === 'development') {
                die('Database connection failed: ' . $e->getMessage());
            } else {
                die('Database connection failed. Please check your configuration.');
            }
        }
    }
}
