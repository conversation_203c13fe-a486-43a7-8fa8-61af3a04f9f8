<?php

namespace App\Config;

/**
 * Database Configuration
 * 
 * Database connection settings for the Web Dashboard System
 */
class Database
{
    public array $default = [
        'hostname' => 'localhost',
        'username' => 'root',
        'password' => '',
        'database' => 'dashboard_system',
        'DBDriver' => 'MySQLi',
        'DBPrefix' => '',
        'pConnect' => false,
        'DBDebug'  => true,
        'charset'  => 'utf8mb4',
        'DBCollat' => 'utf8mb4_general_ci',
        'swapPre'  => '',
        'encrypt'  => false,
        'compress' => false,
        'strictOn' => false,
        'failover' => [],
        'port'     => 3306,
    ];

    public function __construct()
    {
        // Load database settings from environment
        $this->default['hostname'] = env('database.default.hostname', 'localhost');
        $this->default['username'] = env('database.default.username', 'root');
        $this->default['password'] = env('database.default.password', '');
        $this->default['database'] = env('database.default.database', 'dashboard_system');
        $this->default['DBDriver'] = env('database.default.DBDriver', 'MySQLi');
        $this->default['DBPrefix'] = env('database.default.DBPrefix', '');
        $this->default['port'] = (int) env('database.default.port', 3306);
    }

    /**
     * Get database connection
     */
    public function getConnection(string $group = 'default')
    {
        $config = $this->default;
        
        if ($group !== 'default' && isset($this->$group)) {
            $config = $this->$group;
        }

        try {
            $dsn = "mysql:host={$config['hostname']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
            $pdo = new \PDO($dsn, $config['username'], $config['password'], [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                \PDO::ATTR_EMULATE_PREPARES => false,
            ]);
            
            return $pdo;
        } catch (\PDOException $e) {
            if (env('CI_ENVIRONMENT') === 'development') {
                die('Database connection failed: ' . $e->getMessage());
            } else {
                die('Database connection failed. Please check your configuration.');
            }
        }
    }
}
