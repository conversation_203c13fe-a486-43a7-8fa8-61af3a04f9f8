<?php
/**
 * Troubleshooting Script
 * 
 * This script helps diagnose Apache and PHP configuration issues
 */

echo "<h1>🔧 Web Dashboard System - Troubleshooting</h1>";

// Check if we can execute PHP
echo "<div style='background: #f0f8f0; padding: 15px; border-left: 4px solid green; margin: 10px 0;'>";
echo "<h3>✅ PHP Execution Working</h3>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";

// Check file permissions
echo "<h3>📁 File System Check</h3>";
$currentDir = __DIR__;
$isWritable = is_writable($currentDir);
$isReadable = is_readable($currentDir);

echo "<p><strong>Current Directory:</strong> $currentDir</p>";
echo "<p><strong>Readable:</strong> " . ($isReadable ? '✅ Yes' : '❌ No') . "</p>";
echo "<p><strong>Writable:</strong> " . ($isWritable ? '✅ Yes' : '❌ No') . "</p>";

// Check Apache modules
echo "<h3>🔧 Apache Modules</h3>";
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    $requiredModules = ['mod_rewrite', 'mod_headers', 'mod_dir'];
    
    foreach ($requiredModules as $module) {
        $loaded = in_array($module, $modules);
        $status = $loaded ? '✅ Loaded' : '❌ Not loaded';
        echo "<p><strong>$module:</strong> $status</p>";
    }
} else {
    echo "<p>⚠️ Cannot check Apache modules (not running under Apache or function not available)</p>";
}

// Check .htaccess files
echo "<h3>📄 .htaccess Status</h3>";
$htaccessFiles = [
    '.htaccess' => 'Main .htaccess file',
    '.htaccess.backup' => 'Backup .htaccess file',
    '.htaccess.disabled' => 'Disabled .htaccess file',
    '.htaccess.simple' => 'Simple .htaccess file'
];

foreach ($htaccessFiles as $file => $description) {
    $exists = file_exists($file);
    $status = $exists ? '✅ Exists' : '❌ Not found';
    echo "<p><strong>$description ($file):</strong> $status</p>";
}

// Check server variables
echo "<h3>🌐 Server Information</h3>";
$serverVars = [
    'SERVER_SOFTWARE' => 'Web Server',
    'DOCUMENT_ROOT' => 'Document Root',
    'REQUEST_URI' => 'Request URI',
    'HTTP_HOST' => 'HTTP Host',
    'SERVER_NAME' => 'Server Name',
    'SERVER_PORT' => 'Server Port'
];

foreach ($serverVars as $var => $label) {
    $value = $_SERVER[$var] ?? 'Not set';
    echo "<p><strong>$label:</strong> $value</p>";
}

// Check PHP extensions
echo "<h3>🔌 PHP Extensions</h3>";
$requiredExtensions = ['pdo', 'pdo_mysql', 'mysqli', 'openssl', 'mbstring', 'curl'];

foreach ($requiredExtensions as $ext) {
    $loaded = extension_loaded($ext);
    $status = $loaded ? '✅ Loaded' : '❌ Not loaded';
    echo "<p><strong>$ext:</strong> $status</p>";
}

// Provide next steps
echo "<hr>";
echo "<h3>🚀 Next Steps</h3>";

if (file_exists('.htaccess')) {
    echo "<p>⚠️ .htaccess file is active. If you're getting 'Forbidden' errors:</p>";
    echo "<ol>";
    echo "<li><a href='#' onclick='alert(\"Run fix-apache.bat as Administrator\")'>Run fix-apache.bat</a> to temporarily disable .htaccess</li>";
    echo "<li>Try accessing the site again</li>";
    echo "<li>If it works, the issue is in .htaccess configuration</li>";
    echo "</ol>";
} else {
    echo "<p>✅ No .htaccess file found. Try these direct links:</p>";
    echo "<ul>";
    echo "<li><a href='index.php?route=migrate'>Run Database Migration</a></li>";
    echo "<li><a href='index.php?route=auth/login'>Login Page</a></li>";
    echo "<li><a href='index.php?route=dashboard'>Dashboard</a></li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p><a href='index.html'>Back to Access Test</a> | ";
echo "<a href='db-test.php'>Database Test</a> | ";
echo "<a href='setup-database.php'>Database Setup</a></p>";
?>
