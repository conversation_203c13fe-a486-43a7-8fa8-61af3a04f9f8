<?php
/**
 * Database Connection Test
 * 
 * This file tests various database connection methods to help diagnose issues
 */

echo "<h1>Database Connection Test</h1>";

// Test different hostnames
$hosts = ['127.0.0.1', 'localhost', '::1'];
$username = 'root';
$password = '';
$port = 3306;

foreach ($hosts as $host) {
    echo "<h3>Testing connection to: $host</h3>";
    
    try {
        // Test basic connection
        $dsn = "mysql:host=$host;port=$port";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_TIMEOUT => 5
        ]);
        
        echo "<p style='color: green;'>✅ Connection successful to $host</p>";
        
        // Test if database exists
        $stmt = $pdo->query("SHOW DATABASES LIKE 'dashboard_system'");
        $dbExists = $stmt->fetch();
        
        if ($dbExists) {
            echo "<p style='color: green;'>✅ Database 'dashboard_system' exists</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Database 'dashboard_system' does not exist - will be created during migration</p>";
        }
        
        // Show MySQL version
        $stmt = $pdo->query("SELECT VERSION() as version");
        $version = $stmt->fetch();
        echo "<p>MySQL Version: " . $version['version'] . "</p>";
        
        $pdo = null;
        break; // Use the first working connection
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Connection failed to $host: " . $e->getMessage() . "</p>";
    }
}

echo "<hr>";

// Test Laragon MySQL service status
echo "<h3>Laragon MySQL Service Check</h3>";
echo "<p>If all connections failed above, please check:</p>";
echo "<ol>";
echo "<li>Is Laragon running?</li>";
echo "<li>Is MySQL service started in Laragon?</li>";
echo "<li>Check Laragon MySQL port (default: 3306)</li>";
echo "<li>Try restarting MySQL service in Laragon</li>";
echo "</ol>";

echo "<hr>";

// Show current PHP MySQL extensions
echo "<h3>PHP MySQL Extensions</h3>";
$extensions = ['pdo', 'pdo_mysql', 'mysqli', 'mysql'];
foreach ($extensions as $ext) {
    $loaded = extension_loaded($ext);
    $status = $loaded ? '✅ Loaded' : '❌ Not loaded';
    $color = $loaded ? 'green' : 'red';
    echo "<p style='color: $color;'>$ext: $status</p>";
}

echo "<hr>";
echo "<p><a href='test.php'>Back to Web Server Test</a></p>";
echo "<p><a href='direct-access.php'>Dashboard Direct Access</a></p>";
?>
