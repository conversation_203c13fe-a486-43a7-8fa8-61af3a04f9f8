<?php

namespace App\Models;

/**
 * HostingInfo Model
 * 
 * Handles hosting information management
 */
class HostingInfo extends BaseModel
{
    protected $table = 'hosting_info';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'provider_name', 'plan_name', 'disk_usage', 'ssl_expiry', 'renewal_date',
        'cpanel_url', 'cpanel_username', 'cpanel_password_encrypted', 'notes', 'is_active'
    ];
    
    /**
     * Get active hosting info
     */
    public function getActiveHosting(): ?array
    {
        $result = $this->where(['is_active' => 1]);
        return !empty($result) ? $result[0] : null;
    }
    
    /**
     * Get hosting info with decrypted password
     */
    public function getHostingWithCredentials(int $id, string $masterPassword): ?array
    {
        $hosting = $this->find($id);
        
        if (!$hosting) {
            return null;
        }
        
        // Decrypt cPanel password if exists
        if (!empty($hosting['cpanel_password_encrypted'])) {
            $encryptionService = new \App\Services\EncryptionService();
            $hosting['cpanel_password'] = $encryptionService->decrypt(
                $hosting['cpanel_password_encrypted'], 
                $masterPassword
            );
        }
        
        return $hosting;
    }
    
    /**
     * Update hosting info with encrypted password
     */
    public function updateHostingWithCredentials(int $id, array $data, string $masterPassword): bool
    {
        // Encrypt cPanel password if provided
        if (isset($data['cpanel_password']) && !empty($data['cpanel_password'])) {
            $encryptionService = new \App\Services\EncryptionService();
            $data['cpanel_password_encrypted'] = $encryptionService->encrypt(
                $data['cpanel_password'], 
                $masterPassword
            );
            unset($data['cpanel_password']);
        }
        
        return $this->update($id, $data);
    }
    
    /**
     * Create hosting info with encrypted password
     */
    public function createHostingWithCredentials(array $data, string $masterPassword): int
    {
        // Encrypt cPanel password if provided
        if (isset($data['cpanel_password']) && !empty($data['cpanel_password'])) {
            $encryptionService = new \App\Services\EncryptionService();
            $data['cpanel_password_encrypted'] = $encryptionService->encrypt(
                $data['cpanel_password'], 
                $masterPassword
            );
            unset($data['cpanel_password']);
        }
        
        return $this->insert($data);
    }
}
