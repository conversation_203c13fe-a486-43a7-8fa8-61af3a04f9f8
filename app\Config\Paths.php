<?php

namespace App\Config;

/**
 * Paths Configuration
 * 
 * Holds the paths that are used by the system to
 * locate the main directories, app, system, etc.
 */
class Paths
{
    /**
     * The path to the application directory.
     */
    public string $appDirectory = APPPATH;

    /**
     * The path to the system directory.
     */
    public string $systemDirectory = SYSTEMPATH;

    /**
     * The path to the writable directory.
     */
    public string $writableDirectory = WRITEPATH;

    /**
     * The path to the tests directory
     */
    public string $testsDirectory = ROOTPATH . 'tests';

    /**
     * The path to the views directory
     */
    public string $viewDirectory = APPPATH . 'Views';
}
