<?php

namespace App\Libraries;

use App\Config\Database;

/**
 * Migration Library
 * 
 * Handles database migrations for the Web Dashboard System
 */
class Migration
{
    private $db;
    
    public function __construct()
    {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * Run all migrations
     */
    public function runMigrations(): bool
    {
        try {
            // Create migrations table if it doesn't exist
            $this->createMigrationsTable();
            
            // Run individual migrations
            $this->createUsersTable();
            $this->createHostingInfoTable();
            $this->createSocialMediaAccountsTable();
            $this->createSocialMediaPostsTable();
            $this->createCredentialsTable();
            $this->createEmailNotificationsTable();
            $this->createEcommerceAccountsTable();
            $this->createProductsTable();
            $this->createWebsitesTable();
            
            // Insert default data
            $this->insertDefaultData();
            
            return true;
        } catch (\Exception $e) {
            error_log("Migration error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create migrations tracking table
     */
    private function createMigrationsTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS `migrations` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `migration` varchar(255) NOT NULL,
            `executed_at` datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
        
        $this->db->exec($sql);
    }
    
    /**
     * Check if migration has been run
     */
    private function migrationExists(string $migration): bool
    {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM migrations WHERE migration = ?");
        $stmt->execute([$migration]);
        return $stmt->fetchColumn() > 0;
    }
    
    /**
     * Record migration as executed
     */
    private function recordMigration(string $migration)
    {
        $stmt = $this->db->prepare("INSERT INTO migrations (migration) VALUES (?)");
        $stmt->execute([$migration]);
    }
    
    /**
     * Create users table
     */
    private function createUsersTable()
    {
        if ($this->migrationExists('create_users_table')) {
            return;
        }
        
        $sql = "CREATE TABLE `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL,
            `email` varchar(100) NOT NULL,
            `password_hash` varchar(255) NOT NULL,
            `is_active` tinyint(1) DEFAULT 1,
            `last_login` datetime DEFAULT NULL,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`),
            UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
        
        $this->db->exec($sql);
        $this->recordMigration('create_users_table');
    }
    
    /**
     * Create hosting_info table
     */
    private function createHostingInfoTable()
    {
        if ($this->migrationExists('create_hosting_info_table')) {
            return;
        }
        
        $sql = "CREATE TABLE `hosting_info` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `provider_name` varchar(100) NOT NULL,
            `plan_name` varchar(100) DEFAULT NULL,
            `disk_usage` varchar(50) DEFAULT NULL,
            `ssl_expiry` date DEFAULT NULL,
            `renewal_date` date DEFAULT NULL,
            `cpanel_url` varchar(255) DEFAULT NULL,
            `cpanel_username` varchar(100) DEFAULT NULL,
            `cpanel_password_encrypted` text DEFAULT NULL,
            `notes` text DEFAULT NULL,
            `is_active` tinyint(1) DEFAULT 1,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
        
        $this->db->exec($sql);
        $this->recordMigration('create_hosting_info_table');
    }
    
    /**
     * Create social_media_accounts table
     */
    private function createSocialMediaAccountsTable()
    {
        if ($this->migrationExists('create_social_media_accounts_table')) {
            return;
        }
        
        $sql = "CREATE TABLE `social_media_accounts` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `platform` enum('facebook','instagram','youtube','twitter','linkedin') NOT NULL,
            `account_name` varchar(100) NOT NULL,
            `account_id` varchar(100) DEFAULT NULL,
            `api_key_encrypted` text DEFAULT NULL,
            `api_secret_encrypted` text DEFAULT NULL,
            `access_token_encrypted` text DEFAULT NULL,
            `refresh_token_encrypted` text DEFAULT NULL,
            `last_sync` datetime DEFAULT NULL,
            `sync_enabled` tinyint(1) DEFAULT 1,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `platform_account` (`platform`, `account_name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
        
        $this->db->exec($sql);
        $this->recordMigration('create_social_media_accounts_table');
    }
    
    /**
     * Create social_media_posts table
     */
    private function createSocialMediaPostsTable()
    {
        if ($this->migrationExists('create_social_media_posts_table')) {
            return;
        }
        
        $sql = "CREATE TABLE `social_media_posts` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `account_id` int(11) NOT NULL,
            `platform` enum('facebook','instagram','youtube','twitter','linkedin') NOT NULL,
            `post_id` varchar(100) NOT NULL,
            `content` text DEFAULT NULL,
            `media_url` varchar(500) DEFAULT NULL,
            `media_type` enum('image','video','text','link') DEFAULT 'text',
            `post_date` datetime NOT NULL,
            `engagement_count` int(11) DEFAULT 0,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `platform_post` (`platform`, `post_id`),
            KEY `account_id` (`account_id`),
            FOREIGN KEY (`account_id`) REFERENCES `social_media_accounts` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
        
        $this->db->exec($sql);
        $this->recordMigration('create_social_media_posts_table');
    }

    /**
     * Create credentials table
     */
    private function createCredentialsTable()
    {
        if ($this->migrationExists('create_credentials_table')) {
            return;
        }

        $sql = "CREATE TABLE `credentials` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `platform_name` varchar(100) NOT NULL,
            `username` varchar(100) NOT NULL,
            `password_encrypted` text NOT NULL,
            `login_url` varchar(500) DEFAULT NULL,
            `category` varchar(50) DEFAULT 'general',
            `notes` text DEFAULT NULL,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `category` (`category`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $this->db->exec($sql);
        $this->recordMigration('create_credentials_table');
    }

    /**
     * Create email_notifications table
     */
    private function createEmailNotificationsTable()
    {
        if ($this->migrationExists('create_email_notifications_table')) {
            return;
        }

        $sql = "CREATE TABLE `email_notifications` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `subject` varchar(255) NOT NULL,
            `sender` varchar(255) NOT NULL,
            `snippet` text DEFAULT NULL,
            `received_date` datetime NOT NULL,
            `keywords_matched` varchar(255) DEFAULT NULL,
            `is_read` tinyint(1) DEFAULT 0,
            `email_uid` varchar(100) DEFAULT NULL,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `received_date` (`received_date`),
            KEY `is_read` (`is_read`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $this->db->exec($sql);
        $this->recordMigration('create_email_notifications_table');
    }

    /**
     * Create ecommerce_accounts table
     */
    private function createEcommerceAccountsTable()
    {
        if ($this->migrationExists('create_ecommerce_accounts_table')) {
            return;
        }

        $sql = "CREATE TABLE `ecommerce_accounts` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `platform` enum('amazon','flipkart','shopify','woocommerce') NOT NULL,
            `seller_id` varchar(100) NOT NULL,
            `account_name` varchar(100) NOT NULL,
            `api_key_encrypted` text DEFAULT NULL,
            `api_secret_encrypted` text DEFAULT NULL,
            `marketplace_id` varchar(50) DEFAULT NULL,
            `last_sync` datetime DEFAULT NULL,
            `sync_enabled` tinyint(1) DEFAULT 1,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `platform_seller` (`platform`, `seller_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $this->db->exec($sql);
        $this->recordMigration('create_ecommerce_accounts_table');
    }

    /**
     * Create products table
     */
    private function createProductsTable()
    {
        if ($this->migrationExists('create_products_table')) {
            return;
        }

        $sql = "CREATE TABLE `products` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `account_id` int(11) NOT NULL,
            `platform` enum('amazon','flipkart','shopify','woocommerce') NOT NULL,
            `product_id` varchar(100) NOT NULL,
            `name` varchar(255) NOT NULL,
            `image_url` varchar(500) DEFAULT NULL,
            `sku` varchar(100) DEFAULT NULL,
            `price` decimal(10,2) DEFAULT NULL,
            `rating` decimal(3,2) DEFAULT NULL,
            `stock_status` enum('in_stock','out_of_stock','limited','unknown') DEFAULT 'unknown',
            `last_updated` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `platform_product` (`platform`, `product_id`),
            KEY `account_id` (`account_id`),
            FOREIGN KEY (`account_id`) REFERENCES `ecommerce_accounts` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $this->db->exec($sql);
        $this->recordMigration('create_products_table');
    }

    /**
     * Create websites table
     */
    private function createWebsitesTable()
    {
        if ($this->migrationExists('create_websites_table')) {
            return;
        }

        $sql = "CREATE TABLE `websites` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `site_name` varchar(100) NOT NULL,
            `site_url` varchar(255) NOT NULL,
            `admin_url` varchar(255) NOT NULL,
            `credential_id` int(11) DEFAULT NULL,
            `is_active` tinyint(1) DEFAULT 1,
            `last_checked` datetime DEFAULT NULL,
            `uptime_status` enum('online','offline','unknown') DEFAULT 'unknown',
            `response_time` int(11) DEFAULT NULL,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `credential_id` (`credential_id`),
            FOREIGN KEY (`credential_id`) REFERENCES `credentials` (`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $this->db->exec($sql);
        $this->recordMigration('create_websites_table');
    }

    /**
     * Insert default data
     */
    private function insertDefaultData()
    {
        if ($this->migrationExists('insert_default_data')) {
            return;
        }

        // Insert default admin user
        $passwordHash = password_hash('password', PASSWORD_DEFAULT);
        $stmt = $this->db->prepare("INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)");
        $stmt->execute(['admin', '<EMAIL>', $passwordHash]);

        $this->recordMigration('insert_default_data');
    }
