<?php

namespace App\Controllers;

use App\Libraries\Migration;

/**
 * Migration Controller
 * 
 * Handles database migrations for the Web Dashboard System
 */
class Migrate extends BaseController
{
    /**
     * Override authentication check for migration
     */
    protected function checkAuthentication()
    {
        // Allow migration to run without authentication
    }
    
    /**
     * Run database migrations
     */
    public function index()
    {
        try {
            $migration = new Migration();
            $result = $migration->runMigrations();
            
            if ($result) {
                echo "<h1>Migration Successful</h1>";
                echo "<p>All database tables have been created successfully.</p>";
                echo "<p>Default admin user created:</p>";
                echo "<ul>";
                echo "<li>Username: admin</li>";
                echo "<li>Email: <EMAIL></li>";
                echo "<li>Password: password</li>";
                echo "</ul>";
                echo "<p><strong>Please change the default password after login!</strong></p>";
                echo "<p><a href='" . site_url('dashboard') . "'>Go to Dashboard</a></p>";
            } else {
                echo "<h1>Migration Failed</h1>";
                echo "<p>There was an error creating the database tables.</p>";
                echo "<p>Please check your database configuration and try again.</p>";
            }
        } catch (\Exception $e) {
            echo "<h1>Migration Error</h1>";
            echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p>Please check your database configuration in the .env file.</p>";
        }
    }
    
    /**
     * Check database connection
     */
    public function test()
    {
        try {
            $database = new \App\Config\Database();
            $db = $database->getConnection();
            
            echo "<h1>Database Connection Test</h1>";
            echo "<p>✅ Database connection successful!</p>";
            echo "<p>Server info: " . $db->getAttribute(\PDO::ATTR_SERVER_VERSION) . "</p>";
            echo "<p><a href='" . site_url('migrate') . "'>Run Migrations</a></p>";
        } catch (\Exception $e) {
            echo "<h1>Database Connection Failed</h1>";
            echo "<p>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p>Please check your database configuration in the .env file.</p>";
        }
    }
}
