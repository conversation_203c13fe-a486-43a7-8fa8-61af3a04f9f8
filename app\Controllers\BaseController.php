<?php

namespace App\Controllers;

use App\Config\Database;

/**
 * Base Controller
 * 
 * Base controller class that provides common functionality
 * for all controllers in the Web Dashboard System
 */
class BaseController
{
    protected $db;
    protected $request;
    protected $session;
    
    public function __construct()
    {
        // Initialize database connection
        $database = new Database();
        $this->db = $database->getConnection();
        
        // Initialize request data
        $this->request = $this->initializeRequest();
        
        // Initialize session
        $this->session = $_SESSION;
        
        // Check authentication for protected routes
        $this->checkAuthentication();
    }
    
    /**
     * Initialize request data
     */
    private function initializeRequest(): array
    {
        $request = [
            'method' => $_SERVER['REQUEST_METHOD'],
            'uri' => $_SERVER['REQUEST_URI'],
            'get' => $_GET,
            'post' => $_POST,
            'files' => $_FILES,
            'headers' => getallheaders() ?: [],
        ];
        
        // Parse JSON input for API requests
        if (isset($request['headers']['Content-Type']) && 
            strpos($request['headers']['Content-Type'], 'application/json') !== false) {
            $input = json_decode(file_get_contents('php://input'), true);
            if ($input) {
                $request['json'] = $input;
            }
        }
        
        return $request;
    }
    
    /**
     * Check if user is authenticated
     */
    protected function checkAuthentication()
    {
        $publicRoutes = ['login', 'auth'];
        $currentController = strtolower(basename(str_replace('\\', '/', get_class($this))));
        
        if (!in_array($currentController, $publicRoutes) && !$this->isAuthenticated()) {
            redirect('auth/login');
        }
    }
    
    /**
     * Check if user is authenticated
     */
    protected function isAuthenticated(): bool
    {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
    
    /**
     * Load view with data
     */
    protected function view(string $view, array $data = [], string $layout = null)
    {
        // Extract data variables
        extract($data);

        // Start output buffering
        ob_start();

        // Include the view file
        $viewFile = APPPATH . 'Views/' . str_replace('.', '/', $view) . '.php';

        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            echo "<h1>View Not Found</h1>";
            echo "<p>The view file '{$view}' could not be found.</p>";
        }

        // Get the content and clean the buffer
        $content = ob_get_clean();

        // If layout is specified, wrap content in layout
        if ($layout) {
            $data['content'] = $content;
            extract($data);

            ob_start();
            $layoutFile = APPPATH . 'Views/layouts/' . $layout . '.php';

            if (file_exists($layoutFile)) {
                include $layoutFile;
            } else {
                echo $content; // Fallback to content only
            }

            $content = ob_get_clean();
        }

        // Output the content
        echo $content;
    }
    
    /**
     * Return JSON response
     */
    protected function json(array $data, int $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    /**
     * Validate CSRF token
     */
    protected function validateCSRF(): bool
    {
        $token = $this->request['post']['csrf_token'] ?? '';
        return hash_equals($_SESSION['csrf_token'] ?? '', $token);
    }
    
    /**
     * Sanitize input data
     */
    protected function sanitize($data)
    {
        if (is_array($data)) {
            return array_map([$this, 'sanitize'], $data);
        }
        
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate required fields
     */
    protected function validateRequired(array $data, array $required): array
    {
        $errors = [];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty(trim($data[$field]))) {
                $errors[] = "The {$field} field is required.";
            }
        }
        
        return $errors;
    }
}
