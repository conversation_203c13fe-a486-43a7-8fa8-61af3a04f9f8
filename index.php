<?php

/**
 * CodeIgniter 4 Entry Point
 * 
 * This is the main entry point for the Web Dashboard System
 * Compatible with shared hosting environments
 */

// Define path constants
define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);
define('SYSTEMPATH', FCPATH . 'system' . DIRECTORY_SEPARATOR);
define('APPPATH', FCPATH . 'app' . DIRECTORY_SEPARATOR);
define('WRITEPATH', FCPATH . 'writable' . DIRECTORY_SEPARATOR);
define('ROOTPATH', FCPATH);

// Ensure the current directory is pointing to the front controller's directory
chdir(FCPATH);

// Load environment variables
if (file_exists(FCPATH . '.env')) {
    $lines = file(FCPATH . '.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        if (!array_key_exists($name, $_SERVER) && !array_key_exists($name, $_ENV)) {
            putenv(sprintf('%s=%s', $name, $value));
            $_ENV[$name] = $value;
            $_SERVER[$name] = $value;
        }
    }
}

// Bootstrap the application
require_once APPPATH . 'Config/Paths.php';
require_once APPPATH . 'Config/Bootstrap.php';

// Launch the app
$app = new \App\Config\App();
$app->run();
