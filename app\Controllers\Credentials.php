<?php

namespace App\Controllers;

use App\Models\Credential;

/**
 * Credentials Controller
 * 
 * Manages secure credential storage and retrieval
 */
class Credentials extends BaseController
{
    private $credentialModel;
    
    public function __construct()
    {
        $this->credentialModel = new Credential();
    }
    
    /**
     * Display credentials vault
     */
    public function index()
    {
        $category = $this->request['get']['category'] ?? null;
        $search = $this->request['get']['search'] ?? null;
        
        if ($search) {
            $credentials = $this->credentialModel->search($search);
        } else {
            $credentials = $this->credentialModel->getByCategory($category);
        }
        
        $categories = $this->credentialModel->getCategories();
        $categoryStats = $this->credentialModel->getCountByCategory();
        
        $data = [
            'title' => 'Credential Vault - Web Dashboard System',
            'pageTitle' => 'Credential Vault',
            'currentPage' => 'credentials',
            'user' => [
                'id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'email' => $_SESSION['email']
            ],
            'credentials' => $credentials,
            'categories' => $categories,
            'categoryStats' => $categoryStats,
            'currentCategory' => $category,
            'currentSearch' => $search,
            'error' => flash('error'),
            'success' => flash('success')
        ];
        
        $this->view('credentials/index', $data, 'dashboard');
    }
    
    /**
     * Show add/edit credential form
     */
    public function form($id = null)
    {
        $credential = null;
        
        if ($id) {
            $credential = $this->credentialModel->find($id);
            if (!$credential) {
                set_flash('error', 'Credential not found.');
                redirect('credentials');
            }
        }
        
        $categories = $this->credentialModel->getCategories();
        
        $data = [
            'title' => ($id ? 'Edit' : 'Add') . ' Credential - Web Dashboard System',
            'pageTitle' => ($id ? 'Edit' : 'Add') . ' Credential',
            'currentPage' => 'credentials',
            'user' => [
                'id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'email' => $_SESSION['email']
            ],
            'credential' => $credential,
            'categories' => $categories,
            'isEdit' => !empty($id),
            'error' => flash('error'),
            'success' => flash('success')
        ];
        
        $this->view('credentials/form', $data, 'dashboard');
    }
    
    /**
     * Save credential
     */
    public function save($id = null)
    {
        if ($this->request['method'] !== 'POST') {
            redirect('credentials');
        }
        
        // Validate CSRF token
        if (!$this->validateCSRF()) {
            set_flash('error', 'Invalid security token. Please try again.');
            redirect('credentials/form' . ($id ? '/' . $id : ''));
        }
        
        // Get form data
        $data = [
            'platform_name' => $this->sanitize($this->request['post']['platform_name'] ?? ''),
            'username' => $this->sanitize($this->request['post']['username'] ?? ''),
            'login_url' => $this->sanitize($this->request['post']['login_url'] ?? ''),
            'category' => $this->sanitize($this->request['post']['category'] ?? 'general'),
            'notes' => $this->sanitize($this->request['post']['notes'] ?? '')
        ];
        
        $password = $this->request['post']['password'] ?? '';
        $masterPassword = $this->request['post']['master_password'] ?? '';
        
        // Validate required fields
        $errors = $this->validateRequired($data, ['platform_name', 'username']);
        
        if (!empty($errors)) {
            set_flash('error', implode('<br>', $errors));
            redirect('credentials/form' . ($id ? '/' . $id : ''));
        }
        
        // Validate master password if password is provided
        if (!empty($password)) {
            if (empty($masterPassword)) {
                set_flash('error', 'Master password is required to encrypt the credential password.');
                redirect('credentials/form' . ($id ? '/' . $id : ''));
            }
            
            // For existing credentials, validate master password
            if ($id && !$this->credentialModel->validateMasterPassword($masterPassword)) {
                set_flash('error', 'Invalid master password. Please check your master password and try again.');
                redirect('credentials/form' . ($id ? '/' . $id : ''));
            }
            
            $data['password'] = $password;
        }
        
        try {
            if ($id) {
                // Update existing record
                $result = $this->credentialModel->updateCredential($id, $data, $masterPassword);
                
                if ($result) {
                    set_flash('success', 'Credential updated successfully.');
                } else {
                    set_flash('error', 'Failed to update credential.');
                }
            } else {
                // Create new record
                $result = $this->credentialModel->createCredential($data, $masterPassword);
                
                if ($result) {
                    set_flash('success', 'Credential added successfully.');
                } else {
                    set_flash('error', 'Failed to add credential.');
                }
            }
        } catch (\Exception $e) {
            set_flash('error', 'Error: ' . $e->getMessage());
        }
        
        redirect('credentials');
    }
    
    /**
     * View credential with decrypted password
     */
    public function view($id)
    {
        if (!$id) {
            set_flash('error', 'Invalid credential ID.');
            redirect('credentials');
        }
        
        if ($this->request['method'] === 'POST') {
            // Validate CSRF token
            if (!$this->validateCSRF()) {
                set_flash('error', 'Invalid security token. Please try again.');
                redirect('credentials/view/' . $id);
            }
            
            $masterPassword = $this->request['post']['master_password'] ?? '';
            
            if (empty($masterPassword)) {
                set_flash('error', 'Master password is required to view the credential.');
                redirect('credentials/view/' . $id);
            }
            
            try {
                $credential = $this->credentialModel->getCredentialWithPassword($id, $masterPassword);
                
                if (!$credential) {
                    set_flash('error', 'Credential not found.');
                    redirect('credentials');
                }
                
                if ($credential['password'] === '[Decryption Failed]') {
                    set_flash('error', 'Invalid master password. Please check your master password and try again.');
                    redirect('credentials/view/' . $id);
                }
                
                $data = [
                    'title' => 'View Credential - Web Dashboard System',
                    'pageTitle' => 'View Credential',
                    'currentPage' => 'credentials',
                    'user' => [
                        'id' => $_SESSION['user_id'],
                        'username' => $_SESSION['username'],
                        'email' => $_SESSION['email']
                    ],
                    'credential' => $credential,
                    'showPassword' => true,
                    'error' => flash('error'),
                    'success' => flash('success')
                ];
                
                $this->view('credentials/view', $data, 'dashboard');
                return;
                
            } catch (\Exception $e) {
                set_flash('error', 'Error decrypting credential: ' . $e->getMessage());
            }
        }
        
        // Show master password form
        $credential = $this->credentialModel->find($id);
        
        if (!$credential) {
            set_flash('error', 'Credential not found.');
            redirect('credentials');
        }
        
        $data = [
            'title' => 'View Credential - Web Dashboard System',
            'pageTitle' => 'View Credential',
            'currentPage' => 'credentials',
            'user' => [
                'id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'email' => $_SESSION['email']
            ],
            'credential' => $credential,
            'showPassword' => false,
            'error' => flash('error'),
            'success' => flash('success')
        ];
        
        $this->view('credentials/view', $data, 'dashboard');
    }
    
    /**
     * Delete credential
     */
    public function delete($id)
    {
        if (!$id) {
            set_flash('error', 'Invalid credential ID.');
            redirect('credentials');
        }
        
        $result = $this->credentialModel->delete($id);
        
        if ($result) {
            set_flash('success', 'Credential deleted successfully.');
        } else {
            set_flash('error', 'Failed to delete credential.');
        }
        
        redirect('credentials');
    }
}
