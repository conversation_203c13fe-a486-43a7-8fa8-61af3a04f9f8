<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Login - Web Dashboard System' ?></title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js CDN -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <style>
        [x-cloak] { display: none !important; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full space-y-8 p-8">
        
        <!-- Header -->
        <div class="text-center">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Web Dashboard System</h1>
            <p class="text-gray-600">Sign in to your account</p>
        </div>
        
        <!-- Login Form -->
        <div class="bg-white rounded-lg shadow-md p-8">
            
            <!-- Flash Messages -->
            <?php if (!empty($error)): ?>
                <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                    <?= $error ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                    <?= $success ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="<?= site_url('auth/authenticate') ?>" x-data="{ loading: false }" @submit="loading = true">
                
                <?= csrf_field() ?>
                
                <!-- Username Field -->
                <div class="mb-4">
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                        Username
                    </label>
                    <input type="text" 
                           id="username" 
                           name="username" 
                           required
                           value="<?= old('username') ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Enter your username">
                </div>
                
                <!-- Password Field -->
                <div class="mb-6">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        Password
                    </label>
                    <input type="password" 
                           id="password" 
                           name="password" 
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Enter your password">
                </div>
                
                <!-- Submit Button -->
                <button type="submit" 
                        :disabled="loading"
                        :class="loading ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'"
                        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                    
                    <span x-show="!loading">Sign In</span>
                    <span x-show="loading" x-cloak class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Signing In...
                    </span>
                </button>
            </form>
            
            <!-- Default Credentials Info -->
            <div class="mt-6 p-4 bg-blue-50 rounded-md">
                <h3 class="text-sm font-medium text-blue-800 mb-2">Default Login Credentials:</h3>
                <div class="text-sm text-blue-700">
                    <p><strong>Username:</strong> admin</p>
                    <p><strong>Password:</strong> password</p>
                    <p class="mt-2 text-xs text-blue-600">
                        ⚠️ Please change the default password after first login!
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="text-center text-sm text-gray-600">
            <p>&copy; 2025 Web Dashboard System. All rights reserved.</p>
        </div>
    </div>
    
    <!-- Auto-fill for demo purposes -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-fill demo credentials (remove in production)
            if (window.location.hostname === 'localhost') {
                document.getElementById('username').value = 'admin';
                document.getElementById('password').value = 'password';
            }
        });
    </script>
</body>
</html>
