<?php

namespace App\Config;

/**
 * Application Configuration
 * 
 * Main application class that handles routing and request processing
 */
class App
{
    public string $baseURL;
    public string $indexPage = '';
    public string $timezone = 'UTC';
    
    public function __construct()
    {
        $this->baseURL = getenv('app.baseURL') ?: 'http://localhost/';
        $this->timezone = getenv('app.timezone') ?: 'UTC';
        $this->indexPage = getenv('app.indexPage') ?: '';
    }
    
    /**
     * Run the application
     */
    public function run()
    {
        // Get the current URI
        $uri = $this->getURI();
        
        // Route the request
        $this->route($uri);
    }
    
    /**
     * Get the current URI
     */
    private function getURI(): string
    {
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        
        // Remove query string
        if (($pos = strpos($uri, '?')) !== false) {
            $uri = substr($uri, 0, $pos);
        }
        
        // Remove base path
        $basePath = parse_url($this->baseURL, PHP_URL_PATH) ?? '/';
        if ($basePath !== '/' && strpos($uri, $basePath) === 0) {
            $uri = substr($uri, strlen($basePath));
        }
        
        return '/' . trim($uri, '/');
    }
    
    /**
     * Route the request to appropriate controller
     */
    private function route(string $uri)
    {
        // Default route
        if ($uri === '/' || $uri === '') {
            $uri = '/dashboard';
        }
        
        // Parse URI segments
        $segments = array_filter(explode('/', trim($uri, '/')));
        $controller = ucfirst($segments[0] ?? 'Dashboard');
        $method = $segments[1] ?? 'index';
        $params = array_slice($segments, 2);
        
        // Build controller class name
        $controllerClass = "App\\Controllers\\{$controller}";
        
        // Check if controller exists
        if (!class_exists($controllerClass)) {
            $this->show404();
            return;
        }
        
        // Instantiate controller
        $controllerInstance = new $controllerClass();
        
        // Check if method exists
        if (!method_exists($controllerInstance, $method)) {
            $this->show404();
            return;
        }
        
        // Call the method
        call_user_func_array([$controllerInstance, $method], $params);
    }
    
    /**
     * Show 404 error page
     */
    private function show404()
    {
        http_response_code(404);
        echo "<h1>404 - Page Not Found</h1>";
        echo "<p>The requested page could not be found.</p>";
    }
}
