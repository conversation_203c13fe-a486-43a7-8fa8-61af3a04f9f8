<?php

namespace App\Controllers;

use App\Services\EncryptionService;

/**
 * Test Controller
 * 
 * For testing various system components
 */
class Test extends BaseController
{
    /**
     * Override authentication check for testing
     */
    protected function checkAuthentication()
    {
        // Allow test routes without authentication in development
        if (env('CI_ENVIRONMENT') !== 'development') {
            parent::checkAuthentication();
        }
    }
    
    /**
     * Test encryption service
     */
    public function encryption()
    {
        $encryptionService = new EncryptionService();
        $result = $encryptionService->test();
        
        echo "<h1>Encryption Service Test</h1>";
        
        if ($result['success']) {
            echo "<p style='color: green;'>✅ Encryption test passed!</p>";
            echo "<p><strong>Original:</strong> " . htmlspecialchars($result['original']) . "</p>";
            echo "<p><strong>Encrypted:</strong> " . htmlspecialchars($result['encrypted']) . "</p>";
            echo "<p><strong>Decrypted:</strong> " . htmlspecialchars($result['decrypted']) . "</p>";
            echo "<p><strong>Match:</strong> " . ($result['match'] ? 'Yes' : 'No') . "</p>";
        } else {
            echo "<p style='color: red;'>❌ Encryption test failed!</p>";
            echo "<p><strong>Error:</strong> " . htmlspecialchars($result['error']) . "</p>";
        }
        
        echo "<p><a href='" . site_url('dashboard') . "'>Back to Dashboard</a></p>";
    }
    
    /**
     * Test database connection and show system info
     */
    public function system()
    {
        echo "<h1>System Information</h1>";
        
        // PHP Version
        echo "<h2>PHP Information</h2>";
        echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
        echo "<p><strong>OpenSSL Extension:</strong> " . (extension_loaded('openssl') ? 'Loaded' : 'Not Loaded') . "</p>";
        echo "<p><strong>PDO Extension:</strong> " . (extension_loaded('pdo') ? 'Loaded' : 'Not Loaded') . "</p>";
        echo "<p><strong>cURL Extension:</strong> " . (extension_loaded('curl') ? 'Loaded' : 'Not Loaded') . "</p>";
        echo "<p><strong>IMAP Extension:</strong> " . (extension_loaded('imap') ? 'Loaded' : 'Not Loaded') . "</p>";
        
        // Database Connection
        echo "<h2>Database Connection</h2>";
        try {
            $database = new \App\Config\Database();
            $db = $database->getConnection();
            echo "<p style='color: green;'>✅ Database connection successful!</p>";
            echo "<p><strong>Server Version:</strong> " . $db->getAttribute(\PDO::ATTR_SERVER_VERSION) . "</p>";
            
            // Check if tables exist
            $stmt = $db->query("SHOW TABLES");
            $tables = $stmt->fetchAll(\PDO::FETCH_COLUMN);
            echo "<p><strong>Tables:</strong> " . count($tables) . " found</p>";
            echo "<ul>";
            foreach ($tables as $table) {
                echo "<li>" . htmlspecialchars($table) . "</li>";
            }
            echo "</ul>";
            
        } catch (\Exception $e) {
            echo "<p style='color: red;'>❌ Database connection failed!</p>";
            echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        // Environment
        echo "<h2>Environment</h2>";
        echo "<p><strong>Environment:</strong> " . env('CI_ENVIRONMENT', 'production') . "</p>";
        echo "<p><strong>Base URL:</strong> " . env('app.baseURL', 'Not set') . "</p>";
        echo "<p><strong>Timezone:</strong> " . date_default_timezone_get() . "</p>";
        
        echo "<p><a href='" . site_url('dashboard') . "'>Back to Dashboard</a></p>";
    }
}
