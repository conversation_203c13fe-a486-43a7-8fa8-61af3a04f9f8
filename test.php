<?php
// Simple test file to check web server access
echo "<h1>Web Server Test</h1>";
echo "<p>If you can see this, the web server is working!</p>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Script Path: " . __FILE__ . "</p>";

// Test database connection
try {
    require_once 'app/Config/Database.php';
    $database = new App\Config\Database();
    $db = $database->getConnection();
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>Try Main Application</a></p>";
echo "<p><a href='migrate'>Run Database Migration</a></p>";
?>
