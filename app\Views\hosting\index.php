<!-- Flash Messages -->
<?php if (!empty($error)): ?>
    <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
        <?= $error ?>
    </div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
        <?= $success ?>
    </div>
<?php endif; ?>

<!-- Page Header -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h1 class="text-2xl font-bold text-gray-900">Hosting Overview</h1>
        <p class="text-gray-600">Manage your hosting provider information and monitor key metrics</p>
    </div>
    <a href="<?= site_url('hosting/form') ?>" 
       class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Add Hosting Info
    </a>
</div>

<?php if ($hostingInfo): ?>
    <!-- Hosting Information Card -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h2 class="text-xl font-semibold text-gray-900"><?= htmlspecialchars($hostingInfo['provider_name']) ?></h2>
                <?php if (!empty($hostingInfo['plan_name'])): ?>
                    <p class="text-gray-600"><?= htmlspecialchars($hostingInfo['plan_name']) ?></p>
                <?php endif; ?>
            </div>
            <div class="flex space-x-2">
                <a href="<?= site_url('hosting/form/' . $hostingInfo['id']) ?>" 
                   class="text-blue-600 hover:text-blue-800 p-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                </a>
                <button onclick="confirmDelete(<?= $hostingInfo['id'] ?>)" 
                        class="text-red-600 hover:text-red-800 p-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Hosting Details Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            <!-- Disk Usage -->
            <?php if (!empty($hostingInfo['disk_usage'])): ?>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-500 mb-1">Disk Usage</h3>
                    <p class="text-lg font-semibold text-gray-900"><?= htmlspecialchars($hostingInfo['disk_usage']) ?></p>
                </div>
            <?php endif; ?>
            
            <!-- SSL Expiry -->
            <?php if (!empty($hostingInfo['ssl_expiry'])): ?>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-500 mb-1">SSL Expiry</h3>
                    <p class="text-lg font-semibold text-gray-900"><?= date('M d, Y', strtotime($hostingInfo['ssl_expiry'])) ?></p>
                    <?php 
                    $daysUntilExpiry = ceil((strtotime($hostingInfo['ssl_expiry']) - time()) / (60 * 60 * 24));
                    if ($daysUntilExpiry <= 30): ?>
                        <p class="text-sm text-red-600">Expires in <?= $daysUntilExpiry ?> days</p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <!-- Renewal Date -->
            <?php if (!empty($hostingInfo['renewal_date'])): ?>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-500 mb-1">Renewal Date</h3>
                    <p class="text-lg font-semibold text-gray-900"><?= date('M d, Y', strtotime($hostingInfo['renewal_date'])) ?></p>
                    <?php 
                    $daysUntilRenewal = ceil((strtotime($hostingInfo['renewal_date']) - time()) / (60 * 60 * 24));
                    if ($daysUntilRenewal <= 30): ?>
                        <p class="text-sm text-orange-600">Renews in <?= $daysUntilRenewal ?> days</p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <!-- cPanel Access -->
            <?php if (!empty($hostingInfo['cpanel_url'])): ?>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-500 mb-1">cPanel Access</h3>
                    <div class="flex items-center space-x-2">
                        <a href="<?= htmlspecialchars($hostingInfo['cpanel_url']) ?>" 
                           target="_blank"
                           class="text-blue-600 hover:text-blue-800 flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                            </svg>
                            Open cPanel
                        </a>
                        <button onclick="testConnection(<?= $hostingInfo['id'] ?>)" 
                                class="text-green-600 hover:text-green-800 text-sm">
                            Test Connection
                        </button>
                    </div>
                    <?php if (!empty($hostingInfo['cpanel_username'])): ?>
                        <p class="text-sm text-gray-600 mt-1">Username: <?= htmlspecialchars($hostingInfo['cpanel_username']) ?></p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <!-- Status -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-sm font-medium text-gray-500 mb-1">Status</h3>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $hostingInfo['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                    <?= $hostingInfo['is_active'] ? 'Active' : 'Inactive' ?>
                </span>
            </div>
        </div>
        
        <!-- Notes -->
        <?php if (!empty($hostingInfo['notes'])): ?>
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h3 class="text-sm font-medium text-gray-500 mb-2">Notes</h3>
                <p class="text-gray-700"><?= nl2br(htmlspecialchars($hostingInfo['notes'])) ?></p>
            </div>
        <?php endif; ?>
        
        <!-- Last Updated -->
        <div class="mt-6 pt-6 border-t border-gray-200 text-sm text-gray-500">
            Last updated: <?= date('M d, Y \a\t g:i A', strtotime($hostingInfo['updated_at'])) ?>
        </div>
    </div>
    
<?php else: ?>
    <!-- No Hosting Information -->
    <div class="bg-white rounded-lg shadow-md p-12 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Hosting Information</h3>
        <p class="text-gray-600 mb-6">Get started by adding your hosting provider information.</p>
        <a href="<?= site_url('hosting/form') ?>" 
           class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg inline-flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Hosting Information
        </a>
    </div>
<?php endif; ?>

<!-- Quick Actions -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Quick Actions</h3>
        <div class="space-y-2">
            <a href="<?= site_url('hosting/form') ?>" class="block text-blue-600 hover:text-blue-800">
                → Add New Hosting Provider
            </a>
            <a href="<?= site_url('test/system') ?>" class="block text-blue-600 hover:text-blue-800">
                → View System Information
            </a>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Monitoring</h3>
        <div class="space-y-2">
            <p class="text-gray-600">• SSL Certificate Expiry</p>
            <p class="text-gray-600">• Hosting Renewal Dates</p>
            <p class="text-gray-600">• Disk Usage Tracking</p>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Integration</h3>
        <div class="space-y-2">
            <p class="text-gray-600">• cPanel API (Coming Soon)</p>
            <p class="text-gray-600">• WHM Integration</p>
            <p class="text-gray-600">• Automated Monitoring</p>
        </div>
    </div>
</div>

<script>
function confirmDelete(id) {
    if (confirm('Are you sure you want to delete this hosting information? This action cannot be undone.')) {
        window.location.href = '<?= site_url('hosting/delete/') ?>' + id;
    }
}

function testConnection(id) {
    window.location.href = '<?= site_url('hosting/test-connection/') ?>' + id;
}
</script>
