<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Web Dashboard System' ?></title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js CDN -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Custom CSS -->
    <style>
        [x-cloak] { display: none !important; }
        .sidebar-link.active {
            background-color: #dbeafe;
            color: #1d4ed8;
        }
    </style>
    
    <?= $additionalHead ?? '' ?>
</head>
<body class="bg-gray-100 font-sans">
    <div x-data="{ sidebarOpen: false }" class="flex h-screen bg-gray-100">
        
        <!-- Sidebar -->
        <div :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full'" 
             class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0">
            
            <!-- Sidebar Header -->
            <div class="flex items-center justify-between h-16 px-6 bg-blue-600">
                <h1 class="text-xl font-bold text-white">Dashboard</h1>
                <button @click="sidebarOpen = false" class="text-white lg:hidden">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <!-- Navigation Menu -->
            <nav class="mt-8">
                <div class="px-4 space-y-2">
                    <a href="<?= site_url('dashboard') ?>" 
                       class="sidebar-link flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg <?= ($currentPage ?? '') === 'dashboard' ? 'active' : '' ?>">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                        Overview
                    </a>
                    
                    <a href="<?= site_url('hosting') ?>" 
                       class="sidebar-link flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg <?= ($currentPage ?? '') === 'hosting' ? 'active' : '' ?>">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                        </svg>
                        Hosting Overview
                    </a>
                    
                    <a href="<?= site_url('social') ?>" 
                       class="sidebar-link flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg <?= ($currentPage ?? '') === 'social' ? 'active' : '' ?>">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2M7 4h10M7 4l-2 16h14l-2-16"></path>
                        </svg>
                        Social Media
                    </a>
                    
                    <a href="<?= site_url('credentials') ?>" 
                       class="sidebar-link flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg <?= ($currentPage ?? '') === 'credentials' ? 'active' : '' ?>">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                        Credential Vault
                    </a>
                    
                    <a href="<?= site_url('emails') ?>" 
                       class="sidebar-link flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg <?= ($currentPage ?? '') === 'emails' ? 'active' : '' ?>">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Email Notifications
                    </a>
                    
                    <a href="<?= site_url('products') ?>" 
                       class="sidebar-link flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg <?= ($currentPage ?? '') === 'products' ? 'active' : '' ?>">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                        </svg>
                        Product Tracker
                    </a>
                    
                    <a href="<?= site_url('websites') ?>" 
                       class="sidebar-link flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg <?= ($currentPage ?? '') === 'websites' ? 'active' : '' ?>">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                        </svg>
                        Website Manager
                    </a>
                    
                    <!-- Admin Section -->
                    <div class="pt-4 mt-4 border-t border-gray-200">
                        <p class="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider">Admin</p>
                        <div class="mt-2 space-y-1">
                            <a href="<?= site_url('test/system') ?>" 
                               class="sidebar-link flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                System Info
                            </a>
                            <a href="<?= site_url('test/encryption') ?>" 
                               class="sidebar-link flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                                Test Encryption
                            </a>
                        </div>
                    </div>
                </div>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            
            <!-- Top Header -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between h-16 px-6">
                    <button @click="sidebarOpen = true" class="text-gray-500 lg:hidden">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                    
                    <h2 class="text-xl font-semibold text-gray-800"><?= $pageTitle ?? 'Dashboard' ?></h2>
                    
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600">Welcome, <?= htmlspecialchars($user['username'] ?? 'User') ?></span>
                        <a href="<?= site_url('auth/change-password') ?>" class="text-sm text-blue-600 hover:text-blue-800">Change Password</a>
                        <a href="<?= site_url('auth/logout') ?>" class="text-sm text-red-600 hover:text-red-800">Logout</a>
                    </div>
                </div>
            </header>
            
            <!-- Main Content Area -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
                <?= $content ?? '' ?>
            </main>
        </div>
    </div>
    
    <!-- Overlay for mobile sidebar -->
    <div x-show="sidebarOpen" 
         @click="sidebarOpen = false"
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-40 bg-black bg-opacity-25 lg:hidden"
         x-cloak></div>
         
    <?= $additionalScripts ?? '' ?>
</body>
</html>
