<?php

namespace App\Controllers;

/**
 * Dashboard Controller
 * 
 * Main dashboard controller for the Web Dashboard System
 */
class Dashboard extends BaseController
{
    public function index()
    {
        $data = [
            'title' => 'Web Dashboard System',
            'user' => [
                'id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'email' => $_SESSION['email']
            ],
        ];

        $this->view('dashboard/index', $data);
    }
}
