<?php

namespace App\Controllers;

/**
 * Dashboard Controller
 * 
 * Main dashboard controller for the Web Dashboard System
 */
class Dashboard extends BaseController
{
    public function index()
    {
        // For now, bypass authentication to test the setup
        $data = [
            'title' => 'Web Dashboard System',
            'user' => $_SESSION['user'] ?? null,
        ];
        
        $this->view('dashboard/index', $data);
    }
    
    /**
     * Check if user is authenticated (override for testing)
     */
    protected function isAuthenticated(): bool
    {
        // Temporarily return true for testing
        return true;
    }
}
