<?php
/**
 * Direct Access Helper
 * 
 * This file provides direct access to the dashboard without URL rewriting
 */

echo "<h1>Web Dashboard System - Direct Access</h1>";
echo "<p>Use these links if you're having trouble with URL rewriting:</p>";

$baseUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']);

echo "<ul>";
echo "<li><a href='{$baseUrl}/test.php'>Test Web Server</a></li>";
echo "<li><a href='{$baseUrl}/index.php?route=migrate'>Run Database Migration</a></li>";
echo "<li><a href='{$baseUrl}/index.php?route=auth/login'>Login Page</a></li>";
echo "<li><a href='{$baseUrl}/index.php?route=dashboard'>Dashboard</a></li>";
echo "<li><a href='{$baseUrl}/index.php?route=test/system'>System Information</a></li>";
echo "</ul>";

echo "<hr>";
echo "<h2>Troubleshooting Steps:</h2>";
echo "<ol>";
echo "<li>First, try the 'Test Web Server' link above</li>";
echo "<li>If that works, try 'Run Database Migration'</li>";
echo "<li>Then try the 'Login Page' link</li>";
echo "<li>If URL rewriting isn't working, you can use the direct links above</li>";
echo "</ol>";

echo "<hr>";
echo "<h2>Fix URL Rewriting:</h2>";
echo "<p>If direct links work but normal URLs don't:</p>";
echo "<ol>";
echo "<li>Make sure mod_rewrite is enabled in Apache</li>";
echo "<li>Check that .htaccess files are allowed</li>";
echo "<li>Try renaming .htaccess.simple to .htaccess</li>";
echo "</ol>";
?>
